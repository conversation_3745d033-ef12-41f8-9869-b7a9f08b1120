-- =============================================
-- SCRIPT DE ROLLBACK DE EMERGÊNCIA
-- =============================================
-- Use este script APENAS se algo der errado durante a limpeza
-- e você precisar restaurar as constraints rapidamente
-- =============================================

PRINT '============================================='
PRINT 'SCRIPT DE ROLLBACK DE EMERGÊNCIA'
PRINT '============================================='
PRINT 'Data/Hora: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT ''

PRINT 'ATENÇÃO: Este script irá recriar as constraints de chave estrangeira'
PRINT 'Use apenas se o script principal falhou na recriação das constraints'
PRINT ''

-- Verificar se as constraints existem antes de tentar recriar
PRINT 'Verificando constraints existentes...'

-- 1. RECRIAR CONSTRAINT AlarmData -> Batch
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'fk_BatchId_AlData')
BEGIN
    PRINT 'Recriando constraint fk_BatchId_AlData...'
    ALTER TABLE [dbo].[AlarmData] ADD 
        CONSTRAINT [fk_BatchId_AlData] FOREIGN KEY 
        (
            [B_Id]
        ) REFERENCES [dbo].[Batch] (
            [B_Id]
        ) ON DELETE CASCADE
    PRINT '✅ Constraint fk_BatchId_AlData recriada'
END
ELSE
    PRINT '✅ Constraint fk_BatchId_AlData já existe'

-- 2. RECRIAR CONSTRAINT BatchData -> Batch
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'fk_BatchId_Data')
BEGIN
    PRINT 'Recriando constraint fk_BatchId_Data...'
    ALTER TABLE [dbo].[BatchData] ADD 
        CONSTRAINT [fk_BatchId_Data] FOREIGN KEY 
        (
            [B_Id]
        ) REFERENCES [dbo].[Batch] (
            [B_Id]
        ) ON DELETE CASCADE
    PRINT '✅ Constraint fk_BatchId_Data recriada'
END
ELSE
    PRINT '✅ Constraint fk_BatchId_Data já existe'

-- 3. RECRIAR CONSTRAINT CommissioningData -> Batch
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'fk_BatchId_CVData')
BEGIN
    PRINT 'Recriando constraint fk_BatchId_CVData...'
    ALTER TABLE [dbo].[CommissioningData] ADD 
        CONSTRAINT [fk_BatchId_CVData] FOREIGN KEY 
        (
            [B_Id]
        ) REFERENCES [dbo].[Batch] (
            [B_Id]
        ) ON DELETE CASCADE
    PRINT '✅ Constraint fk_BatchId_CVData recriada'
END
ELSE
    PRINT '✅ Constraint fk_BatchId_CVData já existe'

-- 4. RECRIAR CONSTRAINT Events -> Batch
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'fk_BatchId_Events')
BEGIN
    PRINT 'Recriando constraint fk_BatchId_Events...'
    ALTER TABLE [dbo].[Events] ADD 
        CONSTRAINT [fk_BatchId_Events] FOREIGN KEY 
        (
            [B_Id]
        ) REFERENCES [dbo].[Batch] (
            [B_Id]
        ) ON DELETE CASCADE
    PRINT '✅ Constraint fk_BatchId_Events recriada'
END
ELSE
    PRINT '✅ Constraint fk_BatchId_Events já existe'

-- 5. RECRIAR CONSTRAINT PhaseChrono -> Batch
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'fk_BatchId_Phase')
BEGIN
    PRINT 'Recriando constraint fk_BatchId_Phase...'
    ALTER TABLE [dbo].[PhaseChrono] ADD 
        CONSTRAINT [fk_BatchId_Phase] FOREIGN KEY 
        (
            [B_Id]
        ) REFERENCES [dbo].[Batch] (
            [B_Id]
        ) ON DELETE CASCADE
    PRINT '✅ Constraint fk_BatchId_Phase recriada'
END
ELSE
    PRINT '✅ Constraint fk_BatchId_Phase já existe'

PRINT ''
PRINT 'Verificação final das constraints:'
SELECT 
    OBJECT_NAME(parent_object_id) as Tabela_Filha,
    name as Nome_Constraint,
    OBJECT_NAME(referenced_object_id) as Tabela_Pai,
    'Ativa' as Status
FROM sys.foreign_keys 
WHERE referenced_object_id = OBJECT_ID('Batch')
ORDER BY OBJECT_NAME(parent_object_id)

PRINT ''
PRINT '============================================='
PRINT 'ROLLBACK DE EMERGÊNCIA CONCLUÍDO'
PRINT '============================================='
PRINT 'Todas as constraints foram verificadas e recriadas se necessário.'
PRINT ''
