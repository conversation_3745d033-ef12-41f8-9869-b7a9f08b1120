-- =============================================
-- SCRIPT DE LIMPEZA PARA RESOLVER OVERFLOW DO B_Id
-- =============================================
-- ATENÇÃO: FAÇA BACKUP ANTES DE EXECUTAR!
-- <PERSON><PERSON> script resolve o problema de overflow na tabela Batch
-- resetando o contador IDENTITY e limpando dados relacionados
-- =============================================

PRINT 'Iniciando script de limpeza para resolver overflow do B_Id...'
PRINT 'Data/Hora: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT ''

-- Verificar se existem dados antes da limpeza
PRINT 'Status antes da limpeza:'
SELECT 'Batch' as Tabela, COUNT(*) as Total_Registros FROM Batch
UNION ALL
SELECT 'AlarmData', COUNT(*) FROM AlarmData
UNION ALL
SELECT 'BatchData', COUNT(*) FROM BatchData
UNION ALL
SELECT 'CommissioningData', COUNT(*) FROM CommissioningData
UNION ALL
SELECT 'Events', COUNT(*) FROM Events
UNION ALL
SELECT 'PhaseChrono', COUNT(*) FROM PhaseChrono

PRINT ''
PRINT 'Verificando valor máximo atual do B_Id:'
SELECT 'Valor máximo B_Id' as Info, ISNULL(MAX(B_Id), 0) as Valor FROM Batch

PRINT ''
PRINT '============================================='
PRINT 'INICIANDO LIMPEZA DOS DADOS...'
PRINT '============================================='

-- 1. DESABILITAR CONSTRAINTS DE CHAVE ESTRANGEIRA
PRINT 'Passo 1: Desabilitando constraints de chave estrangeira...'

-- Desabilitar constraint AlarmData -> Batch
IF EXISTS (SELECT * FROM dbo.sysobjects WHERE id = object_id(N'[dbo].[fk_BatchId_AlData]') AND OBJECTPROPERTY(id, N'IsForeignKey') = 1)
BEGIN
    ALTER TABLE [dbo].[AlarmData] DROP CONSTRAINT fk_BatchId_AlData
    PRINT '  - Constraint fk_BatchId_AlData removida'
END

-- Desabilitar constraint BatchData -> Batch
IF EXISTS (SELECT * FROM dbo.sysobjects WHERE id = object_id(N'[dbo].[fk_BatchId_Data]') AND OBJECTPROPERTY(id, N'IsForeignKey') = 1)
BEGIN
    ALTER TABLE [dbo].[BatchData] DROP CONSTRAINT fk_BatchId_Data
    PRINT '  - Constraint fk_BatchId_Data removida'
END

-- Desabilitar constraint CommissioningData -> Batch
IF EXISTS (SELECT * FROM dbo.sysobjects WHERE id = object_id(N'[dbo].[fk_BatchId_CVData]') AND OBJECTPROPERTY(id, N'IsForeignKey') = 1)
BEGIN
    ALTER TABLE [dbo].[CommissioningData] DROP CONSTRAINT fk_BatchId_CVData
    PRINT '  - Constraint fk_BatchId_CVData removida'
END

-- Desabilitar constraint Events -> Batch
IF EXISTS (SELECT * FROM dbo.sysobjects WHERE id = object_id(N'[dbo].[fk_BatchId_Events]') AND OBJECTPROPERTY(id, N'IsForeignKey') = 1)
BEGIN
    ALTER TABLE [dbo].[Events] DROP CONSTRAINT fk_BatchId_Events
    PRINT '  - Constraint fk_BatchId_Events removida'
END

-- Desabilitar constraint PhaseChrono -> Batch
IF EXISTS (SELECT * FROM dbo.sysobjects WHERE id = object_id(N'[dbo].[fk_BatchId_Phase]') AND OBJECTPROPERTY(id, N'IsForeignKey') = 1)
BEGIN
    ALTER TABLE [dbo].[PhaseChrono] DROP CONSTRAINT fk_BatchId_Phase
    PRINT '  - Constraint fk_BatchId_Phase removida'
END

PRINT 'Passo 1: Concluído - Constraints desabilitadas'
PRINT ''

-- 2. LIMPAR DADOS DAS TABELAS DEPENDENTES
PRINT 'Passo 2: Limpando dados das tabelas dependentes...'

-- Limpar AlarmData
DELETE FROM [dbo].[AlarmData]
PRINT '  - Tabela AlarmData limpa: ' + CAST(@@ROWCOUNT AS VARCHAR) + ' registros removidos'

-- Limpar BatchData
DELETE FROM [dbo].[BatchData]
PRINT '  - Tabela BatchData limpa: ' + CAST(@@ROWCOUNT AS VARCHAR) + ' registros removidos'

-- Limpar CommissioningData
DELETE FROM [dbo].[CommissioningData]
PRINT '  - Tabela CommissioningData limpa: ' + CAST(@@ROWCOUNT AS VARCHAR) + ' registros removidos'

-- Limpar Events
DELETE FROM [dbo].[Events]
PRINT '  - Tabela Events limpa: ' + CAST(@@ROWCOUNT AS VARCHAR) + ' registros removidos'

-- Limpar PhaseChrono
DELETE FROM [dbo].[PhaseChrono]
PRINT '  - Tabela PhaseChrono limpa: ' + CAST(@@ROWCOUNT AS VARCHAR) + ' registros removidos'

PRINT 'Passo 2: Concluído - Tabelas dependentes limpas'
PRINT ''

-- 3. LIMPAR TABELA BATCH
PRINT 'Passo 3: Limpando tabela Batch...'
DELETE FROM [dbo].[Batch]
PRINT '  - Tabela Batch limpa: ' + CAST(@@ROWCOUNT AS VARCHAR) + ' registros removidos'
PRINT 'Passo 3: Concluído'
PRINT ''

-- 4. RESETAR IDENTITY DA TABELA BATCH
PRINT 'Passo 4: Resetando contador IDENTITY da tabela Batch...'
DBCC CHECKIDENT('[dbo].[Batch]', RESEED, 0)
PRINT '  - Contador IDENTITY resetado para começar em 1'
PRINT 'Passo 4: Concluído'
PRINT ''

-- 5. RECRIAR CONSTRAINTS DE CHAVE ESTRANGEIRA
PRINT 'Passo 5: Recriando constraints de chave estrangeira...'

-- Recriar constraint AlarmData -> Batch (COM CASCADE DELETE)
ALTER TABLE [dbo].[AlarmData] ADD
    CONSTRAINT [fk_BatchId_AlData] FOREIGN KEY
    (
        [B_Id]
    ) REFERENCES [dbo].[Batch] (
        [B_Id]
    ) ON DELETE CASCADE
PRINT '  - Constraint fk_BatchId_AlData recriada'

-- Recriar constraint BatchData -> Batch (COM CASCADE DELETE)
ALTER TABLE [dbo].[BatchData] ADD
    CONSTRAINT [fk_BatchId_Data] FOREIGN KEY
    (
        [B_Id]
    ) REFERENCES [dbo].[Batch] (
        [B_Id]
    ) ON DELETE CASCADE
PRINT '  - Constraint fk_BatchId_Data recriada'

-- Recriar constraint CommissioningData -> Batch (COM CASCADE DELETE)
ALTER TABLE [dbo].[CommissioningData] ADD
    CONSTRAINT [fk_BatchId_CVData] FOREIGN KEY
    (
        [B_Id]
    ) REFERENCES [dbo].[Batch] (
        [B_Id]
    ) ON DELETE CASCADE
PRINT '  - Constraint fk_BatchId_CVData recriada'

-- Recriar constraint Events -> Batch (COM CASCADE DELETE)
ALTER TABLE [dbo].[Events] ADD
    CONSTRAINT [fk_BatchId_Events] FOREIGN KEY
    (
        [B_Id]
    ) REFERENCES [dbo].[Batch] (
        [B_Id]
    ) ON DELETE CASCADE
PRINT '  - Constraint fk_BatchId_Events recriada'

-- Recriar constraint PhaseChrono -> Batch (COM CASCADE DELETE)
ALTER TABLE [dbo].[PhaseChrono] ADD
    CONSTRAINT [fk_BatchId_Phase] FOREIGN KEY
    (
        [B_Id]
    ) REFERENCES [dbo].[Batch] (
        [B_Id]
    ) ON DELETE CASCADE
PRINT '  - Constraint fk_BatchId_Phase recriada'

PRINT 'Passo 5: Concluído - Constraints recriadas'
PRINT ''

-- 6. VERIFICAÇÃO FINAL
PRINT '============================================='
PRINT 'VERIFICAÇÃO FINAL'
PRINT '============================================='

PRINT 'Status após a limpeza:'
SELECT 'Batch' as Tabela, COUNT(*) as Total_Registros FROM Batch
UNION ALL
SELECT 'AlarmData', COUNT(*) FROM AlarmData
UNION ALL
SELECT 'BatchData', COUNT(*) FROM BatchData
UNION ALL
SELECT 'CommissioningData', COUNT(*) FROM CommissioningData
UNION ALL
SELECT 'Events', COUNT(*) FROM Events
UNION ALL
SELECT 'PhaseChrono', COUNT(*) FROM PhaseChrono

PRINT ''
PRINT 'Verificando se IDENTITY foi resetado:'
-- Inserir um registro de teste para verificar se o IDENTITY está funcionando
INSERT INTO [dbo].[Batch] (S_Id, L_Id, BatchName, ProductNumber, ProductName, NodeName, UserName)
VALUES (1, 1, 'TESTE_RESET', 'TEST001', 'Produto Teste', 'Node01', 'SISTEMA')

SELECT 'Novo B_Id após reset' as Info, B_Id as Valor FROM Batch WHERE BatchName = 'TESTE_RESET'

-- Remover o registro de teste
DELETE FROM [dbo].[Batch] WHERE BatchName = 'TESTE_RESET'

PRINT ''
PRINT '============================================='
PRINT 'SCRIPT CONCLUÍDO COM SUCESSO!'
PRINT '============================================='
PRINT 'O problema de overflow foi resolvido.'
PRINT 'O contador B_Id foi resetado e começará em 1 novamente.'
PRINT 'Todas as constraints foram recriadas corretamente.'
PRINT ''
PRINT 'PRÓXIMOS PASSOS:'
PRINT '1. Teste a criação de novos lotes no IFIX'
PRINT '2. Verifique se o erro de overflow não ocorre mais'
PRINT '3. Monitore o crescimento do B_Id'
PRINT ''
PRINT 'Data/Hora de conclusão: ' + CONVERT(VARCHAR, GETDATE(), 120)
