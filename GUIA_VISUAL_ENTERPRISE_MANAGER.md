# 🖥️ Guia Visual - Como Usar o Enterprise Manager

## 📍 Localizando e Abrindo o Enterprise Manager

### Passo 1: Encontrar o Enterprise Manager
```
Iniciar → Programas → Microsoft SQL Server → Enterprise Manager
```

**Ou procure por:**
- "Enterprise Manager" no menu Iniciar
- "SQL Server Enterprise Manager"
- Ícone com símbolo de banco de dados

### Passo 2: Interface do Enterprise Manager
Quando abrir, você verá uma tela similar a esta estrutura:

```
Enterprise Manager
├── Console Root
    ├── Microsoft SQL Servers
        ├── SQL Server Group
            ├── (local) (Windows NT) - [Nome do Computador]
                ├── Databases
                │   ├── master
                │   ├── model  
                │   ├── msdb
                │   ├── tempdb
                │   ├── [NOME_DO_BANCO_IFIX] ← ESTE É O QUE PROCURAMOS
                │   └── Northwind (exemplo)
                ├── Data Transformation Services
                ├── Management
                ├── Replication
                ├── Security
                └── Support Services
```

---

## 🎯 Identificando o Banco Correto

### Passo 1: Expandir a Árvore
1. Clique no **"+"** ao lado de "Microsoft SQL Servers"
2. Clique no **"+"** ao lado de "SQL Server Group"  
3. Clique no **"+"** ao lado do nome do seu servidor (geralmente nome do computador)
4. Clique no **"+"** ao lado de "Databases"

### Passo 2: Procurar o Banco do IFIX
Procure por bancos com nomes como:
- **IFIX**
- **Batch**
- **Production** 
- **Manufacturing**
- **Plant**
- **Process**
- Ou qualquer nome relacionado ao seu processo

### Passo 3: Testar o Banco
1. Clique com **botão direito** no banco suspeito
2. Escolha **"Query Analyzer"** ou **"New Query"**
3. Execute o script `Script_Identificar_Banco.sql`

---

## 📝 Executando Scripts no Query Analyzer

### Interface do Query Analyzer
```
┌─────────────────────────────────────────────────────────────┐
│ File  Edit  Query  Tools  Window  Help                     │
├─────────────────────────────────────────────────────────────┤
│ [▶] Execute  [⏹] Stop  [📁] Open  [💾] Save               │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  /* Cole seu script SQL aqui */                            │
│  SELECT * FROM Batch                                       │
│                                                             │
│                                                             │
├─────────────────────────────────────────────────────────────┤
│ Results                                                     │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Os resultados aparecerão aqui                          │ │
│ │                                                         │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ Messages                                                    │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Mensagens de status aparecerão aqui                    │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### Como Executar um Script
1. **Copiar**: Abra o arquivo .sql no Bloco de Notas → Ctrl+A → Ctrl+C
2. **Colar**: No Query Analyzer → Ctrl+V
3. **Executar**: Clique no botão ▶ "Execute" ou pressione F5
4. **Verificar**: Veja os resultados na aba "Results" e mensagens na aba "Messages"

---

## 💾 Fazendo Backup pelo Enterprise Manager

### Passo 1: Acessar Backup
1. No Enterprise Manager, clique com **botão direito** no banco de dados
2. Escolha **"All Tasks"**
3. Clique em **"Backup Database..."**

### Passo 2: Configurar Backup
```
┌─────────────────────────────────────────────────────────────┐
│ SQL Server Backup                                           │
├─────────────────────────────────────────────────────────────┤
│ General | Options                                           │
│                                                             │
│ Database: [NOME_DO_BANCO]                                   │
│ Name: [NOME_DO_BANCO] backup                               │
│ Description: [Backup antes da limpeza]                     │
│                                                             │
│ Backup:                                                     │
│ ○ Database - complete                                       │
│ ○ Database - differential                                   │
│ ○ Transaction log                                           │
│                                                             │
│ Destination:                                                │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ C:\Backup\Backup_Antes_Limpeza.bak                     │ │
│ └─────────────────────────────────────────────────────────┘ │
│ [Add...] [Remove]                                           │
│                                                             │
│ [OK] [Cancel] [Help]                                        │
└─────────────────────────────────────────────────────────────┘
```

### Passo 3: Configurações Importantes
- **Backup Type**: Deixe marcado "Database - complete"
- **Destination**: Clique "Add..." e escolha um local seguro
- **Nome do arquivo**: Use um nome descritivo com data

---

## 🔍 Interpretando Resultados dos Scripts

### Script de Identificação - Resultados Esperados

**✅ Banco CORRETO:**
```
Banco_Conectado: IFIX_Database
✅ Tabela Batch encontrada
✅ Tabela AlarmData encontrada  
✅ Tabela BatchData encontrada
✅ Tabela Events encontrada
Total_Registros: 427
Maior_BId: 32750
```

**❌ Banco INCORRETO:**
```
Banco_Conectado: master
❌ Tabela Batch NÃO encontrada
❌ Tabela AlarmData NÃO encontrada
❌ Tabela BatchData NÃO encontrada
❌ Tabela Events NÃO encontrada
```

### Script de Verificação - Resultados Típicos
```
Status antes da limpeza:
Tabela              Total_Registros
Batch               427
AlarmData           1250
BatchData           8500
CommissioningData   320
Events              2100
PhaseChrono         890

Valor máximo B_Id: 32750  ← PROBLEMA AQUI!
```

### Script de Limpeza - Mensagens de Sucesso
```
Passo 1: Concluído - Constraints desabilitadas
Passo 2: Concluído - Tabelas dependentes limpas
  - Tabela AlarmData limpa: 1250 registros removidos
  - Tabela BatchData limpa: 8500 registros removidos
  - Tabela CommissioningData limpa: 320 registros removidos
  - Tabela Events limpa: 2100 registros removidos
  - Tabela PhaseChrono limpa: 890 registros removidos
Passo 3: Concluído
  - Tabela Batch limpa: 427 registros removidos
Passo 4: Concluído
  - Contador IDENTITY resetado para começar em 1
Passo 5: Concluído - Constraints recriadas

SCRIPT CONCLUÍDO COM SUCESSO!
```

---

## 🚨 Sinais de Problema

### ❌ Mensagens de Erro Comuns

**Erro de Permissão:**
```
Server: Msg 262, Level 14, State 1
CREATE TABLE permission denied in database 'IFIX'
```
**Solução**: Execute como administrador

**Banco Não Encontrado:**
```
Server: Msg 208, Level 16, State 1
Invalid object name 'Batch'
```
**Solução**: Conecte-se ao banco correto

**Constraint em Uso:**
```
Server: Msg 3726, Level 16, State 1
Could not drop object 'fk_BatchId_Data' because it is referenced by a FOREIGN KEY constraint
```
**Solução**: Execute o script de rollback

---

## 📞 Checklist de Verificação

Antes de executar qualquer script:

- [ ] Enterprise Manager está aberto
- [ ] Conectado ao banco correto (contém tabela Batch)
- [ ] Script de identificação executou com sucesso
- [ ] Backup foi criado
- [ ] IFIX foi parado
- [ ] Equipe foi avisada

Durante a execução:

- [ ] Script está executando sem erros
- [ ] Mensagens de progresso aparecem
- [ ] Não há mensagens de erro em vermelho

Após a execução:

- [ ] Mensagem "SCRIPT CONCLUÍDO COM SUCESSO!" apareceu
- [ ] Todas as tabelas mostram 0 registros
- [ ] IFIX reiniciou normalmente
- [ ] Novo lote pode ser criado sem erro

---

**💡 Dica**: Mantenha este guia aberto durante todo o processo para consulta rápida!
