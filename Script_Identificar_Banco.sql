-- =============================================
-- SCRIPT PARA IDENTIFICAR O BANCO CORRETO
-- =============================================
-- Execute este script PRIMEIRO para identificar
-- qual banco de dados contém as tabelas do IFIX
-- =============================================

PRINT '============================================='
PRINT 'IDENTIFICAÇÃO DO BANCO DE DADOS CORRETO'
PRINT '============================================='
PRINT 'Data/Hora: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT ''

-- 1. MOSTRAR BANCO ATUAL
PRINT '1. Banco de dados atual:'
SELECT DB_NAME() AS 'Banco_Conectado'

PRINT ''

-- 2. LISTAR TODOS OS BANCOS DISPONÍVEIS
PRINT '2. Todos os bancos de dados disponíveis:'
SELECT name AS 'Nome_Banco' FROM sys.databases WHERE name NOT IN ('master', 'model', 'msdb', 'tempdb')

PRINT ''

-- 3. VERIFICAR SE AS TABELAS DO IFIX EXISTEM NO BANCO ATUAL
PRINT '3. Verificando se as tabelas do IFIX existem neste banco:'

-- Verificar tabela Batch
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'Batch')
    PRINT '   ✅ Tabela Batch encontrada'
ELSE
    PRINT '   ❌ Tabela Batch NÃO encontrada'

-- Verificar tabela AlarmData
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'AlarmData')
    PRINT '   ✅ Tabela AlarmData encontrada'
ELSE
    PRINT '   ❌ Tabela AlarmData NÃO encontrada'

-- Verificar tabela BatchData
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'BatchData')
    PRINT '   ✅ Tabela BatchData encontrada'
ELSE
    PRINT '   ❌ Tabela BatchData NÃO encontrada'

-- Verificar tabela Events
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'Events')
    PRINT '   ✅ Tabela Events encontrada'
ELSE
    PRINT '   ❌ Tabela Events NÃO encontrada'

PRINT ''

-- 4. LISTAR TODAS AS TABELAS DO BANCO ATUAL
PRINT '4. Todas as tabelas no banco atual:'
SELECT name AS 'Nome_Tabela' FROM sys.tables ORDER BY name

PRINT ''

-- 5. SE AS TABELAS EXISTEM, MOSTRAR INFORMAÇÕES BÁSICAS
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'Batch')
BEGIN
    PRINT '5. Informações da tabela Batch:'
    
    -- Mostrar estrutura da tabela Batch
    SELECT 
        COLUMN_NAME as 'Coluna',
        DATA_TYPE as 'Tipo',
        IS_NULLABLE as 'Permite_Nulo',
        COLUMN_DEFAULT as 'Valor_Padrao'
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'Batch'
    ORDER BY ORDINAL_POSITION
    
    PRINT ''
    PRINT 'Quantidade de registros na tabela Batch:'
    SELECT COUNT(*) as 'Total_Registros' FROM Batch
    
    PRINT ''
    PRINT 'Valor atual do B_Id:'
    SELECT 
        ISNULL(MIN(B_Id), 0) as 'Menor_BId',
        ISNULL(MAX(B_Id), 0) as 'Maior_BId',
        COUNT(*) as 'Total_Lotes'
    FROM Batch
END
ELSE
BEGIN
    PRINT '5. ❌ Tabela Batch não encontrada neste banco!'
    PRINT ''
    PRINT 'AÇÃO NECESSÁRIA:'
    PRINT '- Conecte-se ao banco correto que contém as tabelas do IFIX'
    PRINT '- Ou verifique se o nome das tabelas está diferente'
END

PRINT ''

-- 6. VERIFICAR VERSÃO DO SQL SERVER
PRINT '6. Versão do SQL Server:'
SELECT @@VERSION as 'Versao_SQL_Server'

PRINT ''

-- 7. VERIFICAR USUÁRIO CONECTADO
PRINT '7. Usuário conectado:'
SELECT 
    SYSTEM_USER as 'Usuario_Sistema',
    USER_NAME() as 'Usuario_Banco',
    IS_SRVROLEMEMBER('sysadmin') as 'E_Administrador'

PRINT ''
PRINT '============================================='
PRINT 'VERIFICAÇÃO CONCLUÍDA'
PRINT '============================================='
PRINT ''
PRINT 'PRÓXIMOS PASSOS:'
PRINT ''
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'Batch')
BEGIN
    PRINT '✅ BANCO CORRETO IDENTIFICADO!'
    PRINT '   - Este é o banco que contém as tabelas do IFIX'
    PRINT '   - Você pode prosseguir com os scripts de limpeza'
    PRINT '   - Execute primeiro: Script_Verificacao_Previa.sql'
END
ELSE
BEGIN
    PRINT '❌ BANCO INCORRETO!'
    PRINT '   - Este banco NÃO contém as tabelas do IFIX'
    PRINT '   - Conecte-se ao banco correto antes de continuar'
    PRINT '   - Procure por bancos com nomes como: IFIX, Batch, Production, etc.'
END

PRINT ''
