# Instruções para Resolver o Problema de Overflow do B_Id

## 📋 Resumo do Problema

O sistema IFIX está apresentando erro de overflow ao criar novos lotes de produção:
- **Erro**: "Unforeseen Number:6 Description: Overflow Source: Project_RCP_MANAGE"
- **Causa**: A coluna `B_Id` da tabela `Batch` ultrapassou o limite de 32.767 (limite de inteiros de 16 bits no IFIX)
- **Solução**: Limpar os dados históricos e resetar o contador IDENTITY

## ⚠️ ATENÇÕES IMPORTANTES

1. **FAÇA BACKUP COMPLETO** antes de executar qualquer script
2. **PARE O SISTEMA IFIX** durante a execução dos scripts
3. **Execute em horário de baixo movimento** da produção
4. **Teste a criação de novos lotes** após a limpeza

## 📁 Arquivos Criados

1. **Script_Verificacao_Previa.sql** - Verificação do estado atual
2. **Script_Limpeza_Batch_Overflow.sql** - Script principal de limpeza
3. **<PERSON>ript_Rollback_Emergencia.sql** - Rollback em caso de problemas

## 🔍 Análise da Estrutura do Banco

### Tabela Principal
- **Batch**: Tabela principal com `B_Id` auto incremental (IDENTITY)

### Tabelas Dependentes (com chave estrangeira para B_Id)
1. **AlarmData** - Dados de alarmes por lote
2. **BatchData** - Dados históricos do lote  
3. **CommissioningData** - Dados de comissionamento
4. **Events** - Eventos do lote
5. **PhaseChrono** - Cronologia das fases

### Constraints com CASCADE DELETE
Todas as constraints foram configuradas com `ON DELETE CASCADE`, o que significa que ao deletar um registro da tabela `Batch`, todos os registros relacionados nas tabelas dependentes são automaticamente removidos.

## 📝 Procedimento Passo a Passo

### Passo 1: Backup
```sql
BACKUP DATABASE [NomeDoBanco] TO DISK = 'C:\Backup\Backup_Antes_Limpeza.bak'
```

### Passo 2: Verificação Prévia
Execute o arquivo: `Script_Verificacao_Previa.sql`

Este script irá mostrar:
- Quantidade de registros em cada tabela
- Valor atual máximo do B_Id
- Status das constraints
- Últimos lotes criados

### Passo 3: Parar o IFIX
- Pare o sistema IFIX para evitar novos registros durante a limpeza
- Coordene com a equipe de produção

### Passo 4: Executar Limpeza
Execute o arquivo: `Script_Limpeza_Batch_Overflow.sql`

O script irá:
1. Desabilitar constraints de chave estrangeira
2. Limpar dados das tabelas dependentes
3. Limpar tabela Batch
4. Resetar contador IDENTITY para começar em 1
5. Recriar todas as constraints
6. Fazer verificação final

### Passo 5: Verificação e Teste
1. Verifique se o script foi executado com sucesso
2. Reinicie o sistema IFIX
3. Teste a criação de um novo lote
4. Verifique se o erro de overflow não ocorre mais

## 🚨 Em Caso de Problemas

Se algo der errado durante a execução:

1. **Execute o rollback**: `Script_Rollback_Emergencia.sql`
2. **Restaure o backup** se necessário
3. **Verifique os logs** de erro do SQL Server

## 📊 Monitoramento Futuro

Para evitar que o problema se repita:

1. **Monitore o crescimento do B_Id**:
```sql
SELECT MAX(B_Id) as Valor_Atual_BId FROM Batch
```

2. **Configure alertas** quando B_Id ultrapassar 25.000

3. **Implemente rotina de limpeza** periódica de dados antigos

4. **Considere alterar o IFIX** para usar variáveis de 32 bits se possível

## 🔧 Solução Definitiva (Recomendação)

Para uma solução definitiva, recomendo:

1. **Alterar o tipo de dados** de `int` para `bigint` na coluna B_Id
2. **Atualizar o IFIX** para usar variáveis de 32 bits ou 64 bits
3. **Implementar arquivamento** automático de dados antigos

## 📞 Suporte

Em caso de dúvidas ou problemas:
- Verifique os logs do SQL Server
- Execute o script de verificação prévia novamente
- Mantenha o backup sempre disponível

---

**Data de criação**: {data_atual}
**Versão**: 1.0
**Compatibilidade**: SQL Server 2000/2005/2008+
