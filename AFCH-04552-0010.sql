if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[fk_AlDesc]') and OBJECTPROPERTY(id, N'IsForeignKey') = 1)
ALTER TABLE [dbo].[AlarmData] DROP CONSTRAINT fk_AlDesc
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[fk_BatchId_AlData]') and OBJECTPROPERTY(id, N'IsForeignKey') = 1)
ALTER TABLE [dbo].[AlarmData] DROP CONSTRAINT fk_BatchId_AlData
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[fk_BatchId_Data]') and OBJECTPROPERTY(id, N'IsForeignKey') = 1)
ALTER TABLE [dbo].[BatchData] DROP CONSTRAINT fk_BatchId_Data
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[fk_BatchId_CVData]') and OBJECTPROPERTY(id, N'IsForeignKey') = 1)
ALTER TABLE [dbo].[CommissioningData] DROP CONSTRAINT fk_BatchId_CVData
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[fk_BatchId_Events]') and OBJECTPROPERTY(id, N'IsForeignKey') = 1)
ALTER TABLE [dbo].[Events] DROP CONSTRAINT fk_BatchId_Events
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[fk_BatchId_Phase]') and OBJECTPROPERTY(id, N'IsForeignKey') = 1)
ALTER TABLE [dbo].[PhaseChrono] DROP CONSTRAINT fk_BatchId_Phase
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[fk_CVDesc_Data]') and OBJECTPROPERTY(id, N'IsForeignKey') = 1)
ALTER TABLE [dbo].[CommissioningData] DROP CONSTRAINT fk_CVDesc_Data
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[fk_RecipeId]') and OBJECTPROPERTY(id, N'IsForeignKey') = 1)
ALTER TABLE [dbo].[RecipeVersion] DROP CONSTRAINT fk_RecipeId
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[fk_RecipeTyp_SetpointDesc]') and OBJECTPROPERTY(id, N'IsForeignKey') = 1)
ALTER TABLE [dbo].[Lang_RecipeSetpointDesc] DROP CONSTRAINT fk_RecipeTyp_SetpointDesc
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[fk_RecipeTyp_Recipe]') and OBJECTPROPERTY(id, N'IsForeignKey') = 1)
ALTER TABLE [dbo].[Recipe] DROP CONSTRAINT fk_RecipeTyp_Recipe
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[FK_RecipeTyp_ParamRelation]') and OBJECTPROPERTY(id, N'IsForeignKey') = 1)
ALTER TABLE [dbo].[RecipeParamRelation] DROP CONSTRAINT FK_RecipeTyp_ParamRelation
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[fk_RecipeVersion_RecipeData]') and OBJECTPROPERTY(id, N'IsForeignKey') = 1)
ALTER TABLE [dbo].[RecipeData] DROP CONSTRAINT fk_RecipeVersion_RecipeData
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[FK_RecipeVersion_RecipeParameter]') and OBJECTPROPERTY(id, N'IsForeignKey') = 1)
ALTER TABLE [dbo].[RecipeParameter] DROP CONSTRAINT FK_RecipeVersion_RecipeParameter
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[FK_RecipeVersion_RecipeStatus]') and OBJECTPROPERTY(id, N'IsForeignKey') = 1)
ALTER TABLE [dbo].[RecipeStatus] DROP CONSTRAINT FK_RecipeVersion_RecipeStatus
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[RecipeView]') and OBJECTPROPERTY(id, N'IsView') = 1)
drop view [dbo].[RecipeView]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AlarmData]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AlarmData]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AlarmDescription]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AlarmDescription]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[AlarmTypRelation]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[AlarmTypRelation]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[BackupHistory]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[BackupHistory]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[Batch]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[Batch]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[BatchData]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[BatchData]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[BatchDataDesc]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[BatchDataDesc]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[BatchTempValues]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[BatchTempValues]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[Batch_SQL]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[Batch_SQL]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[CommissioningData]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[CommissioningData]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[CommissioningDesc]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[CommissioningDesc]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[Events]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[Events]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[FIXALARMS]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[FIXALARMS]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[Lang_BlocksDI]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[Lang_BlocksDI]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[Lang_BlocksTXT]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[Lang_BlocksTXT]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[Lang_CleaningOperations]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[Lang_CleaningOperations]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[Lang_CleaningSteps]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[Lang_CleaningSteps]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[Lang_Controllers]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[Lang_Controllers]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[Lang_Events]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[Lang_Events]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[Lang_IoList]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[Lang_IoList]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[Lang_Messages]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[Lang_Messages]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[Lang_RecipeSetpointDesc]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[Lang_RecipeSetpointDesc]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[Lang_StaticText]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[Lang_StaticText]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[PhaseChrono]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[PhaseChrono]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[Recipe]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[Recipe]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[RecipeData]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[RecipeData]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[RecipeParamRelation]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[RecipeParamRelation]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[RecipeParameter]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[RecipeParameter]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[RecipeStatus]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[RecipeStatus]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[RecipeTypRelation]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[RecipeTypRelation]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[RecipeVersion]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[RecipeVersion]
GO

if exists (select * from dbo.sysobjects where id = object_id(N'[dbo].[Trends]') and OBJECTPROPERTY(id, N'IsUserTable') = 1)
drop table [dbo].[Trends]
GO

CREATE TABLE [dbo].[AlarmData] (
	[B_Id] [int] NOT NULL ,
	[AlNo] [int] NOT NULL ,
	[DateTimeLast] [datetime] NULL ,
	[DateTimeIn] [datetime] NULL ,
	[AlStatus] [varchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[AlPerfName] [varchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[AlPerfComment] [varchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[AlVerName] [varchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[AlVerComment] [varchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO

CREATE TABLE [dbo].[AlarmDescription] (
	[AlNo] [int] NOT NULL ,
	[AlTagName] [varchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[AlText_Lang0] [varchar] (40) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[AlText_Lang1] [varchar] (40) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO

CREATE TABLE [dbo].[AlarmTypRelation] (
	[T_Id] [int] NOT NULL ,
	[AlNo] [int] NOT NULL 
) ON [PRIMARY]
GO

CREATE TABLE [dbo].[BackupHistory] (
	[TableID] [int] IDENTITY (1, 1) NOT NULL ,
	[DateTime] [datetime] NOT NULL ,
	[Backup_MainFolder] [varchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Status_Overall] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Status_SQLBackup] [int] NOT NULL ,
	[Status_SQLDeleted] [int] NOT NULL ,
	[Status_BackHist] [int] NOT NULL ,
	[Status_BackPdfBatch] [int] NOT NULL ,
	[Status_BackPdfRcp] [int] NOT NULL ,
	[SQL_Backupfile] [varchar] (60) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO

CREATE TABLE [dbo].[Batch] (
	[B_Id] [int] IDENTITY (1, 1) NOT NULL ,
	[S_Id] [int] NOT NULL ,
	[L_Id] [int] NOT NULL ,
	[BatchName] [varchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[ProductNumber] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[ProductName] [varchar] (40) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[NodeName] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[UserName] [varchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[EndComment] [varchar] (250) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[EndCommentUser] [varchar] (250) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[EndCommentDateTime] [datetime] NULL ,
	[Deleted] [int] NULL ,
	[BatchStartDateTime] [datetime] NULL ,
	[BatchEndDateTime] [datetime] NULL ,
	[PDFReportOK] [bit] NULL ,
	[PDFReportName] [varchar] (70) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO

CREATE TABLE [dbo].[BatchData] (
	[B_Id] [int] NOT NULL ,
	[LogDateTime] [datetime] NOT NULL ,
	[TagValue1] [varchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TagValue2] [varchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TagValue3] [varchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TagValue4] [varchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TagValue5] [varchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TagValue6] [varchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TagValue7] [varchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TagValue8] [varchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TagValue9] [varchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TagValue10] [varchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TagValue11] [varchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TagValue12] [varchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TagValue13] [varchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TagValue14] [varchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TagValue15] [varchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TagValue16] [varchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TagValue17] [varchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TagValue18] [varchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TagValue19] [varchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TagValue20] [varchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO

CREATE TABLE [dbo].[BatchDataDesc] (
	[T_Id] [int] NOT NULL ,
	[TagNo] [int] NOT NULL ,
	[iFixTagName] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[TagUnit] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[TagDecimal] [int] NOT NULL ,
	[LowLim] [int] NOT NULL ,
	[HighLim] [int] NOT NULL 
) ON [PRIMARY]
GO

CREATE TABLE [dbo].[BatchTempValues] (
	[HistIntTime_MPP] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[HistIntTime_GPP] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[HistIntTime_SPA] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[HistIntTime_SPB] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[HistIntTime_WAS] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[HistIntTime_DIS] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[HistIntTime_GRA] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO

CREATE TABLE [dbo].[Batch_SQL] (
	[sqlname] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[sqlcmd] [varchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO

CREATE TABLE [dbo].[CommissioningData] (
	[B_Id] [int] NOT NULL ,
	[CVNo] [int] NOT NULL ,
	[CVValue] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO

CREATE TABLE [dbo].[CommissioningDesc] (
	[CVNo] [int] NOT NULL ,
	[CVText_Lang0] [varchar] (40) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[CVText_Lang1] [varchar] (40) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[CVUnit] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO

CREATE TABLE [dbo].[Events] (
	[B_Id] [int] NOT NULL ,
	[DateTimeLast] [datetime] NULL ,
	[Message_Type] [varchar] (11) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TagName] [varchar] (40) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Fix_Value] [varchar] (40) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[EvDescription] [varchar] (480) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[EvPerfName] [varchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[EvPerfComment] [varchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[EvVerName] [varchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[EvVerComment] [varchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[DateTimeIn] [datetime] NULL ,
	[ALM_Tagdesc] [varchar] (40) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Unit] [varchar] (13) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO

CREATE TABLE [dbo].[FIXALARMS] (
	[DateTimeLast] [datetime] NULL ,
	[Message_Type] [char] (11) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Tag_Name] [char] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Description] [char] (480) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Fix_Value] [char] (40) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Unit] [char] (13) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Operator_Name] [char] (32) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[ALM_TAGDESC] [char] (40) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[ALM_PERFNAME] [char] (32) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[ALM_PERFBYCOMMENT] [char] (170) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[ALM_VERNAME] [char] (32) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[ALM_VERBYCOMMENT] [char] (170) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[DateTimeIn] [datetime] NULL 
) ON [PRIMARY]
GO

CREATE TABLE [dbo].[Lang_BlocksDI] (
	[iFixTagname] [varchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[DescOpen_Lang0] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[DescClose_Lang0] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[DescOpen_Lang1] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[DescClose_Lang1] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL 
) ON [PRIMARY]
GO

CREATE TABLE [dbo].[Lang_BlocksTXT] (
	[iFixTagName] [varchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[TS0_Lang0] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TS1_Lang0] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TS2_Lang0] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TS3_Lang0] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TS4_Lang0] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TS5_Lang0] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TS6_Lang0] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TS7_Lang0] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TS8_Lang0] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TS9_Lang0] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TS0_Lang1] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TS1_Lang1] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TS2_Lang1] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TS3_Lang1] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TS4_Lang1] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TS5_Lang1] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TS6_Lang1] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TS7_Lang1] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TS8_Lang1] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TS9_Lang1] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO

CREATE TABLE [dbo].[Lang_CleaningOperations] (
	[OperationNo] [varchar] (2) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Desc_Lang0] [varchar] (40) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Desc_Lang1] [varchar] (40) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO

CREATE TABLE [dbo].[Lang_CleaningSteps] (
	[StepNo] [varchar] (2) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Desc_Lang0] [varchar] (60) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Desc_Lang1] [varchar] (60) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL 
) ON [PRIMARY]
GO

CREATE TABLE [dbo].[Lang_Controllers] (
	[iFixTagName] [varchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Desc_Lang0] [varchar] (40) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Desc_Lang1] [varchar] (40) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO

CREATE TABLE [dbo].[Lang_Events] (
	[iFixTagName] [varchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Desc_Lang0] [varchar] (40) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Desc_Lang1] [varchar] (40) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL 
) ON [PRIMARY]
GO

CREATE TABLE [dbo].[Lang_IoList] (
	[iFixTagName] [varchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Desc_Lang0] [varchar] (40) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Desc_Lang1] [varchar] (40) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL 
) ON [PRIMARY]
GO

CREATE TABLE [dbo].[Lang_Messages] (
	[iFixTagname] [varchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Desc_Lang0] [varchar] (40) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[Desc_Lang1] [varchar] (40) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL 
) ON [PRIMARY]
GO

CREATE TABLE [dbo].[Lang_RecipeSetpointDesc] (
	[T_Id] [int] NOT NULL ,
	[SetpointNo] [varchar] (5) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[SetpointText_Lang0] [varchar] (40) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[SetpointText_Lang1] [varchar] (40) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO

CREATE TABLE [dbo].[Lang_StaticText] (
	[IFixTagName] [varchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Desc_Lang0] [varchar] (40) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Desc_Lang1] [varchar] (40) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO

CREATE TABLE [dbo].[PhaseChrono] (
	[B_Id] [int] NOT NULL ,
	[PhaseNo] [int] NOT NULL ,
	[StartDateTime] [datetime] NULL ,
	[EndDateTime] [datetime] NULL 
) ON [PRIMARY]
GO

CREATE TABLE [dbo].[Recipe] (
	[R_Id] [int] IDENTITY (1, 1) NOT NULL ,
	[T_Id] [int] NOT NULL ,
	[RecipeName] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[RecipeTypDesc] [varchar] (40) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO

CREATE TABLE [dbo].[RecipeData] (
	[V_Id] [int] NOT NULL ,
	[PhaseNo] [varchar] (2) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[SetpointNo] [varchar] (5) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[SetpointDesc] [varchar] (40) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[SetpointValue] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[SetpointLoadingValue] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[SetpointUnit] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO

CREATE TABLE [dbo].[RecipeParamRelation] (
	[T_Id] [int] NOT NULL ,
	[CVNo] [int] NOT NULL 
) ON [PRIMARY]
GO

CREATE TABLE [dbo].[RecipeParameter] (
	[V_Id] [int] NOT NULL ,
	[CVNo] [int] NOT NULL ,
	[CVValue] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[CVLoadingValue] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[CVDesc] [varchar] (40) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[CVUnit] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO

CREATE TABLE [dbo].[RecipeStatus] (
	[S_Id] [int] IDENTITY (1, 1) NOT NULL ,
	[V_Id] [int] NULL ,
	[Status] [int] NULL ,
	[StatusDesc] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[Author] [varchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[DateTime] [datetime] NULL ,
	[InUse] [int] NULL 
) ON [PRIMARY]
GO

CREATE TABLE [dbo].[RecipeTypRelation] (
	[T_Id] [int] NOT NULL ,
	[RecipeTyp] [varchar] (5) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL 
) ON [PRIMARY]
GO

CREATE TABLE [dbo].[RecipeVersion] (
	[V_Id] [int] IDENTITY (1, 1) NOT NULL ,
	[R_Id] [int] NOT NULL ,
	[Version] [int] NOT NULL ,
	[RecipeNumber] [varchar] (20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[RecipeDescr] [varchar] (40) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL 
) ON [PRIMARY]
GO

CREATE TABLE [dbo].[Trends] (
	[TagCategory] [varchar] (10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL ,
	[TagName] [varchar] (30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL ,
	[TagDecimal] [int] NULL ,
	[LowLim] [int] NULL ,
	[HighLim] [int] NULL 
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[AlarmDescription] ADD 
	CONSTRAINT [PK_AlarmDescription] PRIMARY KEY  CLUSTERED 
	(
		[AlNo]
	) WITH  FILLFACTOR = 90  ON [PRIMARY] 
GO

ALTER TABLE [dbo].[BackupHistory] ADD 
	CONSTRAINT [PK_BackupHistory_1__56] PRIMARY KEY  CLUSTERED 
	(
		[TableID]
	) WITH  FILLFACTOR = 90  ON [PRIMARY] 
GO

ALTER TABLE [dbo].[Batch] ADD 
	CONSTRAINT [pk_BatchName] PRIMARY KEY  CLUSTERED 
	(
		[B_Id]
	) WITH  FILLFACTOR = 90  ON [PRIMARY] 
GO

ALTER TABLE [dbo].[CommissioningDesc] ADD 
	CONSTRAINT [PK_CommissioningDesc] PRIMARY KEY  CLUSTERED 
	(
		[CVNo]
	) WITH  FILLFACTOR = 90  ON [PRIMARY] 
GO

ALTER TABLE [dbo].[Recipe] ADD 
	CONSTRAINT [pk_RecipeName] PRIMARY KEY  CLUSTERED 
	(
		[R_Id]
	) WITH  FILLFACTOR = 90  ON [PRIMARY] 
GO

ALTER TABLE [dbo].[RecipeStatus] ADD 
	CONSTRAINT [PK_RecipeStatus] PRIMARY KEY  CLUSTERED 
	(
		[S_Id]
	) WITH  FILLFACTOR = 90  ON [PRIMARY] 
GO

ALTER TABLE [dbo].[RecipeTypRelation] ADD 
	CONSTRAINT [PK_RecipeTypRelation] PRIMARY KEY  CLUSTERED 
	(
		[T_Id]
	) WITH  FILLFACTOR = 90  ON [PRIMARY] 
GO

ALTER TABLE [dbo].[RecipeVersion] ADD 
	CONSTRAINT [pk_RecipeVersion] PRIMARY KEY  CLUSTERED 
	(
		[V_Id]
	) WITH  FILLFACTOR = 90  ON [PRIMARY] 
GO

ALTER TABLE [dbo].[AlarmData] ADD 
	CONSTRAINT [fk_AlDesc] FOREIGN KEY 
	(
		[AlNo]
	) REFERENCES [dbo].[AlarmDescription] (
		[AlNo]
	),
	CONSTRAINT [fk_BatchId_AlData] FOREIGN KEY 
	(
		[B_Id]
	) REFERENCES [dbo].[Batch] (
		[B_Id]
	) ON DELETE CASCADE 
GO

ALTER TABLE [dbo].[BatchData] ADD 
	CONSTRAINT [fk_BatchId_Data] FOREIGN KEY 
	(
		[B_Id]
	) REFERENCES [dbo].[Batch] (
		[B_Id]
	) ON DELETE CASCADE 
GO

ALTER TABLE [dbo].[CommissioningData] ADD 
	CONSTRAINT [fk_BatchId_CVData] FOREIGN KEY 
	(
		[B_Id]
	) REFERENCES [dbo].[Batch] (
		[B_Id]
	) ON DELETE CASCADE ,
	CONSTRAINT [fk_CVDesc_Data] FOREIGN KEY 
	(
		[CVNo]
	) REFERENCES [dbo].[CommissioningDesc] (
		[CVNo]
	)
GO

ALTER TABLE [dbo].[Events] ADD 
	CONSTRAINT [fk_BatchId_Events] FOREIGN KEY 
	(
		[B_Id]
	) REFERENCES [dbo].[Batch] (
		[B_Id]
	) ON DELETE CASCADE 
GO

ALTER TABLE [dbo].[Lang_RecipeSetpointDesc] ADD 
	CONSTRAINT [fk_RecipeTyp_SetpointDesc] FOREIGN KEY 
	(
		[T_Id]
	) REFERENCES [dbo].[RecipeTypRelation] (
		[T_Id]
	)
GO

ALTER TABLE [dbo].[PhaseChrono] ADD 
	CONSTRAINT [fk_BatchId_Phase] FOREIGN KEY 
	(
		[B_Id]
	) REFERENCES [dbo].[Batch] (
		[B_Id]
	) ON DELETE CASCADE 
GO

ALTER TABLE [dbo].[Recipe] ADD 
	CONSTRAINT [fk_RecipeTyp_Recipe] FOREIGN KEY 
	(
		[T_Id]
	) REFERENCES [dbo].[RecipeTypRelation] (
		[T_Id]
	)
GO

ALTER TABLE [dbo].[RecipeData] ADD 
	CONSTRAINT [fk_RecipeVersion_RecipeData] FOREIGN KEY 
	(
		[V_Id]
	) REFERENCES [dbo].[RecipeVersion] (
		[V_Id]
	) ON DELETE CASCADE  ON UPDATE CASCADE 
GO

ALTER TABLE [dbo].[RecipeParamRelation] ADD 
	CONSTRAINT [FK_RecipeTyp_ParamRelation] FOREIGN KEY 
	(
		[T_Id]
	) REFERENCES [dbo].[RecipeTypRelation] (
		[T_Id]
	)
GO

ALTER TABLE [dbo].[RecipeParameter] ADD 
	CONSTRAINT [FK_RecipeVersion_RecipeParameter] FOREIGN KEY 
	(
		[V_Id]
	) REFERENCES [dbo].[RecipeVersion] (
		[V_Id]
	) ON DELETE CASCADE  ON UPDATE CASCADE 
GO

ALTER TABLE [dbo].[RecipeStatus] ADD 
	CONSTRAINT [FK_RecipeVersion_RecipeStatus] FOREIGN KEY 
	(
		[V_Id]
	) REFERENCES [dbo].[RecipeVersion] (
		[V_Id]
	) ON DELETE CASCADE  ON UPDATE CASCADE 
GO

ALTER TABLE [dbo].[RecipeVersion] ADD 
	CONSTRAINT [fk_RecipeId] FOREIGN KEY 
	(
		[R_Id]
	) REFERENCES [dbo].[Recipe] (
		[R_Id]
	) ON DELETE CASCADE  ON UPDATE CASCADE 
GO

SET QUOTED_IDENTIFIER ON 
GO
SET ANSI_NULLS ON 
GO

CREATE VIEW dbo.RecipeView
AS
SELECT     TOP 100 PERCENT dbo.Recipe.RecipeTypDesc, dbo.Recipe.R_Id, dbo.RecipeVersion.V_Id, dbo.RecipeVersion.Version, dbo.RecipeStatus.S_Id, 
                      dbo.RecipeStatus.StatusDesc, dbo.Recipe.RecipeName, dbo.RecipeVersion.RecipeNumber, dbo.RecipeVersion.RecipeDescr, dbo.RecipeStatus.Author, 
                      dbo.RecipeStatus.DateTime
FROM         dbo.Recipe INNER JOIN
                      dbo.RecipeVersion ON dbo.Recipe.R_Id = dbo.RecipeVersion.R_Id INNER JOIN
                      dbo.RecipeStatus ON dbo.RecipeVersion.V_Id = dbo.RecipeStatus.V_Id INNER JOIN
                      dbo.RecipeTypRelation ON dbo.Recipe.T_Id = dbo.RecipeTypRelation.T_Id
ORDER BY dbo.Recipe.RecipeTypDesc, dbo.Recipe.RecipeName

GO
SET QUOTED_IDENTIFIER OFF 
GO
SET ANSI_NULLS ON 
GO

