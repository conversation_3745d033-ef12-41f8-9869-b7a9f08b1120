# 📋 Passo a Passo - Como Executar os Scripts SQL

## 🎯 Guia Completo para Iniciantes em SQL Server

Este guia foi criado para quem não tem muita experiência com SQL Server. Siga cada passo cuidadosamente.

---

## 🔧 PARTE 1: PREPARAÇÃO

### Passo 1.1: Localizar o Enterprise Manager
1. No computador onde está o SQL Server, procure por:
   - **Enterprise Manager** (SQL Server 2000)
   - Ou **SQL Server Management Studio** (versões mais novas)
2. Geralmente está em: `Iniciar > Programas > Microsoft SQL Server`

### Passo 1.2: Conectar ao Banco de Dados
1. Abra o Enterprise Manager
2. Expanda o servidor local (geralmente aparece como nome do computador)
3. Expanda "Databases"
4. Localize o banco de dados do IFIX (pode ter nome como "IFIX", "Batch", "Production", etc.)
5. Clique com botão direito no banco → **"Query Analyzer"** ou **"New Query"**

### Passo 1.3: Verificar Conexão
Na janela que abrir, digite e execute:
```sql
SELECT DB_NAME() AS 'Banco Atual'
```
- Clique no botão **"Execute"** (ou pressione F5)
- Deve mostrar o nome do banco de dados

---

## 🔍 PARTE 2: VERIFICAÇÃO INICIAL

### Passo 2.1: Executar Verificação Prévia
1. Abra o arquivo `Script_Verificacao_Previa.sql` no Bloco de Notas
2. **Copie TODO o conteúdo** do arquivo (Ctrl+A, depois Ctrl+C)
3. **Cole** na janela do Query Analyzer (Ctrl+V)
4. Clique em **"Execute"** ou pressione **F5**

### Passo 2.2: Analisar Resultados
O script mostrará várias informações. **ANOTE** os seguintes valores:

**Quantidade de registros:**
- Batch: _____ registros
- AlarmData: _____ registros  
- BatchData: _____ registros
- CommissioningData: _____ registros
- Events: _____ registros
- PhaseChrono: _____ registros

**Valor atual do B_Id:**
- Valor máximo: _____ 

⚠️ **Se o valor máximo for maior que 30.000, o problema é urgente!**

---

## 💾 PARTE 3: BACKUP (OBRIGATÓRIO!)

### Passo 3.1: Fazer Backup
1. **Feche** o Query Analyzer
2. No Enterprise Manager, clique com botão direito no banco de dados
3. Escolha **"All Tasks"** → **"Backup Database"**

### Passo 3.2: Configurar Backup
1. **Destination**: Clique em "Add" e escolha um local (ex: `C:\Backup\`)
2. **Nome do arquivo**: `Backup_Antes_Limpeza_AAAAMMDD.bak`
   - Substitua AAAAMMDD pela data atual (ex: `Backup_Antes_Limpeza_20241201.bak`)
3. **Backup Type**: Database - Complete
4. Clique **"OK"**

### Passo 3.3: Aguardar Conclusão
- O backup pode demorar alguns minutos
- **NÃO CONTINUE** até o backup terminar
- Anote o local onde foi salvo: ________________

---

## ⏸️ PARTE 4: PARAR O SISTEMA IFIX

### Passo 4.1: Coordenar com a Equipe
- **AVISE** a equipe de produção que o sistema será parado
- **ESCOLHA** um horário de baixo movimento
- **CONFIRME** que não há processos críticos rodando

### Passo 4.2: Parar o IFIX
1. Feche todas as telas do IFIX
2. Pare os serviços do IFIX:
   - Vá em `Iniciar > Executar > services.msc`
   - Procure por serviços com nome "IFIX" ou "GE"
   - Clique com botão direito → **"Stop"**

### Passo 4.3: Verificar se Parou
- Aguarde 2-3 minutos
- Verifique se não há mais processos do IFIX rodando

---

## 🧹 PARTE 5: EXECUTAR A LIMPEZA

### Passo 5.1: Abrir Nova Consulta
1. Volte ao Enterprise Manager
2. Clique com botão direito no banco → **"Query Analyzer"**
3. Verifique se está conectado ao banco correto

### Passo 5.2: Executar Script Principal
1. Abra o arquivo `Script_Limpeza_Batch_Overflow.sql`
2. **Copie TODO o conteúdo** (Ctrl+A, Ctrl+C)
3. **Cole** no Query Analyzer (Ctrl+V)
4. **LEIA** o script uma vez para entender o que fará
5. Clique em **"Execute"** ou pressione **F5**

### Passo 5.3: Acompanhar Execução
- O script mostrará mensagens de progresso
- **NÃO INTERROMPA** a execução
- Pode demorar alguns minutos dependendo da quantidade de dados
- Anote qualquer mensagem de erro

### Passo 5.4: Verificar Sucesso
No final, o script deve mostrar:
- "SCRIPT CONCLUÍDO COM SUCESSO!"
- Todas as tabelas com 0 registros
- Novo B_Id deve ser 1

---

## ✅ PARTE 6: VERIFICAÇÃO E TESTE

### Passo 6.1: Verificar Limpeza
Execute esta consulta para confirmar:
```sql
SELECT 'Batch' as Tabela, COUNT(*) as Registros FROM Batch
UNION ALL
SELECT 'AlarmData', COUNT(*) FROM AlarmData
```
**Resultado esperado**: Todas as tabelas com 0 registros

### Passo 6.2: Reiniciar o IFIX
1. Vá em `services.msc` novamente
2. Inicie os serviços do IFIX que foram parados
3. Abra o sistema IFIX normalmente

### Passo 6.3: Teste Crítico
1. **Tente criar um novo lote** no IFIX
2. **Verifique** se não aparece mais o erro de overflow
3. **Confirme** que o lote foi criado com sucesso

### Passo 6.4: Verificar no Banco
Execute esta consulta para ver o novo lote:
```sql
SELECT B_Id, BatchName, ProductNumber FROM Batch ORDER BY B_Id DESC
```
**Resultado esperado**: Deve mostrar o novo lote com B_Id = 1

---

## 🚨 EM CASO DE PROBLEMAS

### Se der erro durante a execução:
1. **NÃO ENTRE EM PÂNICO**
2. **ANOTE** a mensagem de erro completa
3. Execute o `Script_Rollback_Emergencia.sql`
4. Se necessário, restaure o backup

### Para restaurar backup:
1. Enterprise Manager → botão direito no banco
2. "All Tasks" → "Restore Database"
3. Escolha o arquivo de backup criado
4. Clique "OK"

### Se o IFIX não iniciar:
1. Verifique se todos os serviços estão rodando
2. Reinicie o computador se necessário
3. Teste a conexão com o banco de dados

---

## 📞 CHECKLIST FINAL

Antes de considerar concluído, verifique:

- [ ] Backup foi criado com sucesso
- [ ] Script de limpeza executou sem erros
- [ ] Todas as tabelas estão vazias
- [ ] IFIX reiniciou normalmente
- [ ] Novo lote foi criado com sucesso (B_Id = 1)
- [ ] Erro de overflow não aparece mais
- [ ] Equipe foi avisada que sistema voltou ao normal

---

## 📝 ANOTAÇÕES

Use este espaço para anotar informações importantes durante o processo:

**Data da execução**: _______________

**Horário início**: _______________

**Horário fim**: _______________

**Problemas encontrados**: 
_________________________________
_________________________________

**Observações**:
_________________________________
_________________________________

---

**⚠️ LEMBRE-SE**: Em caso de dúvida, PARE e peça ajuda. É melhor ser cauteloso!
