-- =============================================
-- SCRIPT DE VERIFICAÇÃO PRÉVIA
-- =============================================
-- Execute este script ANTES do script de limpeza
-- para verificar o estado atual do banco de dados
-- =============================================

PRINT '============================================='
PRINT 'VERIFICAÇÃO PRÉVIA DO BANCO DE DADOS'
PRINT '============================================='
PRINT 'Data/Hora: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT ''

-- 1. VERIFICAR VERSÃO DO SQL SERVER
PRINT '1. Versão do SQL Server:'
SELECT @@VERSION as Versao_SQL_Server

PRINT ''

-- 2. VERIFICAR QUANTIDADE DE REGISTROS EM CADA TABELA
PRINT '2. Quantidade de registros por tabela:'
SELECT 'Batch' as Tabela, COUNT(*) as Total_Registros FROM Batch
UNION ALL
SELECT 'AlarmData', COUNT(*) FROM AlarmData  
UNION ALL
SELECT 'BatchData', COUNT(*) FROM BatchData
UNION ALL
SELECT 'CommissioningData', COUNT(*) FROM CommissioningData
UNION ALL
SELECT 'Events', COUNT(*) FROM Events
UNION ALL
SELECT 'PhaseChrono', COUNT(*) FROM PhaseChrono

PRINT ''

-- 3. VERIFICAR VALOR ATUAL DO B_Id
PRINT '3. Análise do B_Id na tabela Batch:'
SELECT 
    'Valor mínimo' as Tipo, 
    ISNULL(MIN(B_Id), 0) as Valor 
FROM Batch
UNION ALL
SELECT 
    'Valor máximo', 
    ISNULL(MAX(B_Id), 0) 
FROM Batch
UNION ALL
SELECT 
    'Próximo IDENTITY', 
    ISNULL(IDENT_CURRENT('Batch'), 0)

PRINT ''

-- 4. VERIFICAR SE ESTÁ PRÓXIMO DO LIMITE DE 32767
DECLARE @MaxBId INT
SELECT @MaxBId = ISNULL(MAX(B_Id), 0) FROM Batch

PRINT '4. Verificação de proximidade do limite:'
IF @MaxBId > 30000
    PRINT '   ⚠️  ATENÇÃO: B_Id está próximo do limite de 32767! Valor atual: ' + CAST(@MaxBId AS VARCHAR)
ELSE IF @MaxBId > 25000
    PRINT '   ⚠️  AVISO: B_Id está se aproximando do limite. Valor atual: ' + CAST(@MaxBId AS VARCHAR)
ELSE
    PRINT '   ✅ B_Id ainda está em nível seguro. Valor atual: ' + CAST(@MaxBId AS VARCHAR)

PRINT ''

-- 5. VERIFICAR CONSTRAINTS EXISTENTES
PRINT '5. Constraints de chave estrangeira relacionadas ao B_Id:'
SELECT 
    OBJECT_NAME(parent_object_id) as Tabela_Filha,
    name as Nome_Constraint,
    OBJECT_NAME(referenced_object_id) as Tabela_Pai
FROM sys.foreign_keys 
WHERE referenced_object_id = OBJECT_ID('Batch')
ORDER BY OBJECT_NAME(parent_object_id)

PRINT ''

-- 6. VERIFICAR ÚLTIMOS REGISTROS CRIADOS
PRINT '6. Últimos 5 lotes criados:'
SELECT TOP 5 
    B_Id,
    BatchName,
    ProductNumber,
    ProductName,
    BatchStartDateTime,
    BatchEndDateTime
FROM Batch 
ORDER BY B_Id DESC

PRINT ''

-- 7. VERIFICAR ESPAÇO EM DISCO
PRINT '7. Informações de espaço do banco de dados:'
SELECT 
    name as Nome_Arquivo,
    size/128.0 as Tamanho_MB,
    CASE 
        WHEN max_size = -1 THEN 'Ilimitado'
        ELSE CAST(max_size/128.0 AS VARCHAR) + ' MB'
    END as Tamanho_Maximo,
    growth as Crescimento
FROM sys.database_files

PRINT ''

-- 8. VERIFICAR SE HÁ TRANSAÇÕES ATIVAS
PRINT '8. Verificação de transações ativas:'
SELECT 
    session_id,
    transaction_id,
    name,
    transaction_begin_time,
    transaction_type,
    transaction_state
FROM sys.dm_tran_session_transactions st
INNER JOIN sys.dm_tran_active_transactions at ON st.transaction_id = at.transaction_id
WHERE session_id <> @@SPID

PRINT ''
PRINT '============================================='
PRINT 'VERIFICAÇÃO CONCLUÍDA'
PRINT '============================================='
PRINT ''
PRINT 'RECOMENDAÇÕES:'
PRINT '1. Faça um BACKUP COMPLETO antes de executar a limpeza'
PRINT '2. Execute a limpeza em horário de baixo movimento'
PRINT '3. Coordene com a equipe do IFIX para parar o sistema temporariamente'
PRINT '4. Teste a criação de novos lotes após a limpeza'
PRINT ''
PRINT 'Para fazer backup:'
PRINT 'BACKUP DATABASE [NomeDoBanco] TO DISK = ''C:\Backup\Backup_Antes_Limpeza.bak'''
PRINT ''
